"""
MCP (Model Context Protocol) Server for NewsMonitor AI Chat Agent.

This module implements an MCP server that provides structured access to:
- Articles database with search and filtering capabilities
- Yahoo Finance API for real-time market data
- Market analysis and financial data aggregation

The MCP server acts as a bridge between the AI chat agent and the NewsMonitor data sources,
providing a standardized interface for data access and manipulation.
"""

import json
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List, Optional, Union

from mcp.server import Server
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    Resource,
    Tool,
    TextContent,
    ImageContent,
    EmbeddedResource,
    LoggingLevel
)

from db.database import get_db_manager
from apis.yahoo_finance import yahoo_api
from utils.logging_config import get_api_logger

logger = get_api_logger(__name__)


class NewsMonitorMCPServer:
    """MCP Server for NewsMonitor data access."""

    def __init__(self):
        self.server = Server("newsmonitor-mcp")
        self.db = get_db_manager()
        self._setup_handlers()

    def _setup_handlers(self):
        """Set up MCP server handlers."""
        
        @self.server.list_resources()
        async def handle_list_resources() -> List[Resource]:
            """List available resources."""
            return [
                Resource(
                    uri="newsmonitor://articles",
                    name="Financial Articles",
                    description="Access to financial news articles database",
                    mimeType="application/json"
                ),
                Resource(
                    uri="newsmonitor://market-data",
                    name="Market Data",
                    description="Real-time and historical market data via Yahoo Finance",
                    mimeType="application/json"
                ),
                Resource(
                    uri="newsmonitor://analysis",
                    name="Market Analysis",
                    description="Aggregated market analysis and insights",
                    mimeType="application/json"
                )
            ]

        @self.server.read_resource()
        async def handle_read_resource(uri: str) -> str:
            """Read a specific resource."""
            if uri == "newsmonitor://articles":
                return await self._get_recent_articles()
            elif uri == "newsmonitor://market-data":
                return await self._get_market_overview()
            elif uri == "newsmonitor://analysis":
                return await self._get_market_analysis()
            else:
                raise ValueError(f"Unknown resource: {uri}")

        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """List available tools."""
            return [
                Tool(
                    name="search_articles",
                    description="Search financial articles by keyword, date range, or source",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "start_date": {"type": "string", "description": "Start date (YYYY-MM-DD)"},
                            "end_date": {"type": "string", "description": "End date (YYYY-MM-DD)"},
                            "source": {"type": "string", "description": "News source filter"},
                            "limit": {"type": "integer", "description": "Maximum results", "default": 10}
                        }
                    }
                ),
                Tool(
                    name="get_stock_data",
                    description="Get stock price data for a specific ticker",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "ticker": {"type": "string", "description": "Stock ticker symbol"},
                            "period": {"type": "string", "description": "Time period (1d, 5d, 1mo, etc.)"},
                            "interval": {"type": "string", "description": "Data interval (1m, 5m, 1h, 1d)", "default": "1d"}
                        },
                        "required": ["ticker"]
                    }
                ),
                Tool(
                    name="get_market_overview",
                    description="Get market overview for multiple tickers",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "tickers": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "List of ticker symbols",
                                "default": ["SPY", "QQQ", "IWM", "VTI"]
                            }
                        }
                    }
                ),
                Tool(
                    name="analyze_sentiment",
                    description="Analyze sentiment of recent articles for a specific topic or ticker",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "topic": {"type": "string", "description": "Topic or ticker to analyze"},
                            "days_back": {"type": "integer", "description": "Days to look back", "default": 7}
                        },
                        "required": ["topic"]
                    }
                ),
                Tool(
                    name="get_featured_articles",
                    description="Get featured articles based on influence score",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "limit": {"type": "integer", "description": "Number of articles", "default": 5}
                        }
                    }
                )
            ]

        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """Handle tool calls."""
            try:
                if name == "search_articles":
                    result = await self._search_articles(**arguments)
                elif name == "get_stock_data":
                    result = await self._get_stock_data(**arguments)
                elif name == "get_market_overview":
                    result = await self._get_market_overview_tool(**arguments)
                elif name == "analyze_sentiment":
                    result = await self._analyze_sentiment(**arguments)
                elif name == "get_featured_articles":
                    result = await self._get_featured_articles(**arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")

                return [TextContent(type="text", text=json.dumps(result, indent=2, default=str))]

            except Exception as e:
                logger.error(f"Error in tool {name}: {e}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]

    async def _get_recent_articles(self) -> str:
        """Get recent articles."""
        try:
            from web.data.news_data import get_all_news
            articles = get_all_news(limit=20)
            return json.dumps(articles, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error getting recent articles: {e}")
            return json.dumps({"error": str(e)})

    async def _get_market_overview(self) -> str:
        """Get market overview."""
        try:
            tickers = ["SPY", "QQQ", "IWM", "VTI", "^VIX"]
            overview = {}
            
            for ticker in tickers:
                try:
                    data = yahoo_api.get_latest_price(ticker)
                    if not data.empty:
                        latest = data.iloc[-1]
                        overview[ticker] = {
                            "price": float(latest['Close']),
                            "volume": int(latest['Volume']),
                            "timestamp": latest.name.isoformat()
                        }
                except Exception as e:
                    logger.warning(f"Error getting data for {ticker}: {e}")
                    overview[ticker] = {"error": str(e)}
            
            return json.dumps(overview, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error getting market overview: {e}")
            return json.dumps({"error": str(e)})

    async def _get_market_analysis(self) -> str:
        """Get market analysis."""
        try:
            # Get recent predictions and analysis
            from web.data.prediction_service import get_prediction_history
            predictions = get_prediction_history(limit=5)
            
            analysis = {
                "recent_predictions": predictions,
                "timestamp": datetime.now().isoformat()
            }
            
            return json.dumps(analysis, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error getting market analysis: {e}")
            return json.dumps({"error": str(e)})

    async def _search_articles(
        self,
        query: Optional[str] = None,
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        source: Optional[str] = None,
        limit: int = 10
    ) -> Dict[str, Any]:
        """Search articles with filters."""
        try:
            from web.data.news_data import get_all_news
            
            articles = get_all_news(
                limit=limit,
                start_date=start_date,
                end_date=end_date,
                search_keyword=query,
                source=source
            )
            
            return articles
        except Exception as e:
            logger.error(f"Error searching articles: {e}")
            return {"error": str(e)}

    async def _get_stock_data(
        self,
        ticker: str,
        period: Optional[str] = None,
        interval: str = "1d"
    ) -> Dict[str, Any]:
        """Get stock data for a ticker."""
        try:
            if period:
                data = yahoo_api.get_price_data(ticker=ticker, period=period, interval=interval)
            else:
                # Default to 5 days
                end_date = datetime.now()
                start_date = end_date - timedelta(days=5)
                data = yahoo_api.get_price_data(
                    ticker=ticker,
                    start_date=start_date.strftime('%Y-%m-%d'),
                    end_date=end_date.strftime('%Y-%m-%d'),
                    interval=interval
                )
            
            if data.empty:
                return {"error": f"No data available for {ticker}"}
            
            # Convert to JSON-serializable format
            result = {
                "ticker": ticker,
                "data": data.reset_index().to_dict(orient='records')
            }
            
            return result
        except Exception as e:
            logger.error(f"Error getting stock data for {ticker}: {e}")
            return {"error": str(e)}

    async def _get_market_overview_tool(self, tickers: List[str] = None) -> Dict[str, Any]:
        """Get market overview for specified tickers."""
        if not tickers:
            tickers = ["SPY", "QQQ", "IWM", "VTI"]
        
        try:
            overview = {}
            for ticker in tickers:
                try:
                    data = yahoo_api.get_latest_price(ticker)
                    if not data.empty:
                        latest = data.iloc[-1]
                        overview[ticker] = {
                            "price": float(latest['Close']),
                            "volume": int(latest['Volume']),
                            "high": float(latest['High']),
                            "low": float(latest['Low']),
                            "open": float(latest['Open']),
                            "timestamp": latest.name.isoformat()
                        }
                except Exception as e:
                    overview[ticker] = {"error": str(e)}
            
            return overview
        except Exception as e:
            logger.error(f"Error getting market overview: {e}")
            return {"error": str(e)}

    async def _analyze_sentiment(self, topic: str, days_back: int = 7) -> Dict[str, Any]:
        """Analyze sentiment for a topic."""
        try:
            from web.data.news_data import get_all_news
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            articles = get_all_news(
                search_keyword=topic,
                start_date=start_date.strftime('%Y-%m-%d'),
                end_date=end_date.strftime('%Y-%m-%d'),
                limit=50
            )
            
            # Basic sentiment analysis based on article metadata
            sentiment_counts = {"positive": 0, "negative": 0, "neutral": 0}
            total_articles = len(articles.get('all', []))
            
            for article in articles.get('all', []):
                # This is a simplified sentiment analysis
                # In a real implementation, you'd use the existing sentiment analysis
                sentiment = article.get('article_metadata', {}).get('sentiment', 'neutral')
                if sentiment in sentiment_counts:
                    sentiment_counts[sentiment] += 1
                else:
                    sentiment_counts['neutral'] += 1
            
            return {
                "topic": topic,
                "period": f"{days_back} days",
                "total_articles": total_articles,
                "sentiment_distribution": sentiment_counts,
                "articles": articles
            }
        except Exception as e:
            logger.error(f"Error analyzing sentiment for {topic}: {e}")
            return {"error": str(e)}

    async def _get_featured_articles(self, limit: int = 5) -> Dict[str, Any]:
        """Get featured articles."""
        try:
            from web.data.news_data import get_featured_news
            featured = get_featured_news(limit=limit)
            return featured
        except Exception as e:
            logger.error(f"Error getting featured articles: {e}")
            return {"error": str(e)}

    async def run(self):
        """Run the MCP server."""
        async with stdio_server() as (read_stream, write_stream):
            await self.server.run(
                read_stream,
                write_stream,
                InitializationOptions(
                    server_name="newsmonitor-mcp",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=None,
                        experimental_capabilities=None,
                    ),
                ),
            )


async def main():
    """Main entry point for the MCP server."""
    server = NewsMonitorMCPServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
