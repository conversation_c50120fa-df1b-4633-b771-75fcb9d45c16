-- Migration: Add chat tables for AI chat functionality
-- Created: 2025-06-28

-- Create chat_sessions table
CREATE TABLE IF NOT EXISTS chat_sessions (
    id VARCHAR PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    session_token VARCHAR NOT NULL UNIQUE,
    title VARCHAR,
    status VA<PERSON><PERSON>R DEFAULT 'active',
    current_page_url VARCHAR,
    page_context JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    last_activity_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS chat_messages (
    id VARCHAR PRIMARY KEY,
    session_id VARCHAR NOT NULL REFERENCES chat_sessions(id) ON DELETE CASCADE,
    role VARCHAR NOT NULL,
    content TEXT NOT NULL,
    message_type <PERSON><PERSON><PERSON>R DEFAULT 'text',
    context_data JSONB,
    model_used VARCHAR,
    processing_time FLOAT,
    token_count INTEGER,
    cost FLOAT,
    status VA<PERSON>HAR DEFAULT 'sent',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create indexes for chat_sessions
CREATE INDEX IF NOT EXISTS ix_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS ix_chat_sessions_session_token ON chat_sessions(session_token);
CREATE INDEX IF NOT EXISTS ix_chat_sessions_status ON chat_sessions(status);
CREATE INDEX IF NOT EXISTS ix_chat_sessions_created_at ON chat_sessions(created_at);
CREATE INDEX IF NOT EXISTS ix_chat_sessions_last_activity ON chat_sessions(last_activity_at);

-- Create indexes for chat_messages
CREATE INDEX IF NOT EXISTS ix_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX IF NOT EXISTS ix_chat_messages_role ON chat_messages(role);
CREATE INDEX IF NOT EXISTS ix_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS ix_chat_messages_status ON chat_messages(status);
CREATE INDEX IF NOT EXISTS ix_chat_messages_model ON chat_messages(model_used);

-- Add updated_at trigger for chat_sessions
CREATE OR REPLACE FUNCTION update_chat_sessions_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER chat_sessions_updated_at_trigger
    BEFORE UPDATE ON chat_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_chat_sessions_updated_at();

-- Add updated_at trigger for chat_messages
CREATE OR REPLACE FUNCTION update_chat_messages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER chat_messages_updated_at_trigger
    BEFORE UPDATE ON chat_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_chat_messages_updated_at();
