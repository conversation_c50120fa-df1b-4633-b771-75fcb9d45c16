# AI Chat Agent Integration for NewsMonitor

This document describes the AI chat agent integration that has been added to the NewsMonitor website, providing users with an intelligent assistant for financial news analysis and market data queries.

## Overview

The AI chat agent is a comprehensive system that includes:

- **Real-time chat interface** with WebSocket communication
- **Context-aware responses** using current page content
- **Integration with existing LLM APIs** (Gemini, OpenAI, Anthropic)
- **Access to financial data** via Yahoo Finance API
- **Persistent chat sessions** with message history
- **Model Context Protocol (MCP)** for structured data access

## Architecture

### Components

1. **Database Layer** (`db/`)
   - `chat_service.py` - Chat session and message management
   - `models.py` - Extended with ChatSession and ChatMessage models
   - `migrations/add_chat_tables.sql` - Database schema for chat functionality

2. **MCP Server** (`mcp/`)
   - `server.py` - Model Context Protocol server for structured data access
   - Provides tools for article search, market data, and sentiment analysis

3. **AI Chat Service** (`web/chat/`)
   - `ai_service.py` - Core AI chat logic and LLM integration
   - `websocket_handler.py` - Real-time WebSocket communication
   - `routes.py` - REST API endpoints for chat functionality

4. **Frontend** (`web/static/`)
   - `css/chat-widget.css` - Responsive chat widget styling
   - `js/chat-widget.js` - Chat interface and WebSocket client
   - `js/context-extractor.js` - Page content extraction for context

## Features

### Chat Widget
- **Floating chat button** positioned in bottom-right corner
- **Expandable chat window** with message history
- **Typing indicators** and real-time message delivery
- **Suggested questions** based on current page content
- **Mobile responsive** design with dark mode support

### Context Awareness
- **Automatic page content extraction** including articles, market data, and metadata
- **Real-time context updates** when navigating between pages
- **Intelligent content summarization** for AI context injection
- **Ticker symbol detection** from page content

### AI Capabilities
- **Multi-provider LLM support** (Gemini, OpenAI, Anthropic)
- **Financial domain expertise** with market analysis capabilities
- **Article summarization** and sentiment analysis
- **Market data queries** and trend analysis
- **Educational responses** focused on financial literacy

### Session Management
- **Persistent chat sessions** across page navigation
- **Anonymous user support** with session tokens
- **Authenticated user integration** with user accounts
- **Message history** and conversation continuity
- **Session statistics** including token usage and costs

## Installation

### Prerequisites
- Python 3.8+
- PostgreSQL database
- Redis (for WebSocket scaling, optional)
- LLM API keys (Gemini, OpenAI, or Anthropic)

### Dependencies
Add to your requirements.txt:
```
Flask-SocketIO>=5.3.0
python-socketio>=5.8.0
mcp>=1.0.0
```

### Database Setup
1. Run the migration script:
```sql
psql -d your_database -f db/migrations/add_chat_tables.sql
```

2. Or use SQLAlchemy to create tables:
```python
from db.models import Base
from db.database import get_db_manager
Base.metadata.create_all(bind=get_db_manager().connection.engine)
```

### Configuration
Set environment variables for LLM providers:
```bash
# Gemini
GOOGLE_API_KEY=your_gemini_api_key

# OpenAI
OPENAI_API_KEY=your_openai_api_key

# Anthropic
ANTHROPIC_API_KEY=your_anthropic_api_key
```

## Usage

### Starting the Application
The chat functionality is automatically integrated when you start the Flask application:

```bash
python web/app.py
```

The chat widget will appear on all pages and connect automatically via WebSocket.

### API Endpoints

#### REST API
- `POST /api/chat/session` - Create new chat session
- `GET /api/chat/session/<id>` - Get session details
- `GET /api/chat/session/<id>/history` - Get message history
- `POST /api/chat/message` - Send message (synchronous)
- `DELETE /api/chat/session/<id>` - Delete session

#### WebSocket Events
- `join_session` - Join a chat session
- `send_message` - Send a message
- `typing` - Typing indicator
- `update_context` - Update page context
- `get_suggestions` - Request suggested questions

### Testing
Run the integration test suite:
```bash
python test_chat_integration.py
```

## Customization

### AI Behavior
Modify the system prompt in `web/chat/ai_service.py`:
```python
def _get_system_prompt(self) -> str:
    return """Your custom system prompt here..."""
```

### Chat Widget Appearance
Customize styling in `web/static/css/chat-widget.css`:
- Colors and themes
- Position and sizing
- Animation effects
- Mobile responsiveness

### Context Extraction
Enhance page content extraction in `web/static/js/context-extractor.js`:
- Add new content selectors
- Implement custom extraction logic
- Add metadata extraction

### LLM Provider Selection
Configure default provider in `web/chat/ai_service.py`:
```python
self.default_provider = 'gemini'  # or 'openai', 'anthropic'
```

## Security Considerations

### Authentication
- Anonymous users get session tokens for temporary access
- Authenticated users have persistent session history
- Admin users have access to all chat analytics

### Rate Limiting
- Built-in rate limiting in LLM API managers
- WebSocket connection limits
- Message frequency controls

### Data Privacy
- Chat messages are stored in the database
- User context is temporarily cached
- No sensitive financial advice is provided

## Monitoring and Analytics

### Session Statistics
- Message count and token usage
- Processing time metrics
- Cost tracking per session
- Error rate monitoring

### Performance Metrics
- WebSocket connection health
- LLM response times
- Context extraction performance
- Database query optimization

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check if Socket.IO is properly installed
   - Verify CORS configuration
   - Ensure Redis is running (if using)

2. **AI Responses Not Working**
   - Verify LLM API keys are set
   - Check API rate limits and quotas
   - Review system prompt configuration

3. **Context Not Updating**
   - Check JavaScript console for errors
   - Verify page content selectors
   - Test context extraction manually

4. **Database Errors**
   - Ensure chat tables are created
   - Check database connection
   - Verify user permissions

### Debug Mode
Enable debug logging:
```python
import logging
logging.getLogger('web.chat').setLevel(logging.DEBUG)
```

## Future Enhancements

### Planned Features
- **Multi-language support** for international users
- **Voice input/output** capabilities
- **Chart analysis** with image recognition
- **Portfolio integration** for personalized advice
- **Advanced analytics** dashboard for admins

### Integration Opportunities
- **Email notifications** for important market alerts
- **Calendar integration** for earnings dates and events
- **Social media sentiment** analysis
- **News alert subscriptions** based on chat preferences

## Support

For issues and questions:
1. Check the troubleshooting section above
2. Review the test suite output
3. Check application logs for errors
4. Verify all dependencies are installed correctly

The AI chat integration is designed to enhance user engagement with intelligent, context-aware financial assistance while maintaining the existing NewsMonitor functionality.
