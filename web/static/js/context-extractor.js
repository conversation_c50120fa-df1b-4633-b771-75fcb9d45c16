/**
 * Page Context Extractor for AI Chat
 * 
 * Extracts relevant content from the current page to provide context
 * for AI responses, including articles, market data, and page metadata.
 */

class PageContextExtractor {
    constructor() {
        this.observers = [];
        this.lastContext = null;
        this.contextChangeCallbacks = [];
        this.init();
    }

    init() {
        this.setupObservers();
        this.bindEvents();
    }

    setupObservers() {
        // Observe changes to article content
        const articlesContainer = document.querySelector('.news-container, .articles-container, main');
        if (articlesContainer) {
            const observer = new MutationObserver(() => {
                this.notifyContextChange();
            });
            
            observer.observe(articlesContainer, {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['class', 'data-*']
            });
            
            this.observers.push(observer);
        }

        // Observe URL changes (for SPA navigation)
        let lastUrl = location.href;
        const urlObserver = new MutationObserver(() => {
            const url = location.href;
            if (url !== lastUrl) {
                lastUrl = url;
                setTimeout(() => this.notifyContextChange(), 500); // Delay to allow content to load
            }
        });
        
        urlObserver.observe(document, { subtree: true, childList: true });
        this.observers.push(urlObserver);
    }

    bindEvents() {
        // Listen for page load events
        window.addEventListener('load', () => this.notifyContextChange());
        
        // Listen for hash changes
        window.addEventListener('hashchange', () => this.notifyContextChange());
        
        // Listen for popstate (back/forward navigation)
        window.addEventListener('popstate', () => {
            setTimeout(() => this.notifyContextChange(), 100);
        });
    }

    extractContext() {
        const context = {
            url: window.location.href,
            title: document.title,
            timestamp: new Date().toISOString(),
            page_type: this.detectPageType(),
            metadata: this.extractMetadata()
        };

        // Extract content based on page type
        switch (context.page_type) {
            case 'news':
                context.articles = this.extractArticles();
                context.featured_articles = this.extractFeaturedArticles();
                break;
            case 'market_analysis':
                context.market_data = this.extractMarketData();
                context.charts = this.extractChartData();
                break;
            case 'admin':
                context.admin_data = this.extractAdminData();
                break;
            default:
                context.content = this.extractGeneralContent();
        }

        // Extract visible text content
        context.visible_text = this.extractVisibleText();
        
        // Extract any financial tickers mentioned
        context.tickers = this.extractTickers(context.visible_text);

        return context;
    }

    detectPageType() {
        const path = window.location.pathname.toLowerCase();
        
        if (path.includes('market-analysis') || path.includes('analysis')) {
            return 'market_analysis';
        } else if (path.includes('admin')) {
            return 'admin';
        } else if (path === '/' || path.includes('news')) {
            return 'news';
        }
        
        return 'general';
    }

    extractMetadata() {
        const metadata = {};
        
        // Extract meta tags
        document.querySelectorAll('meta[name], meta[property]').forEach(meta => {
            const name = meta.getAttribute('name') || meta.getAttribute('property');
            const content = meta.getAttribute('content');
            if (name && content) {
                metadata[name] = content;
            }
        });

        // Extract structured data
        document.querySelectorAll('script[type="application/ld+json"]').forEach(script => {
            try {
                const data = JSON.parse(script.textContent);
                metadata.structured_data = metadata.structured_data || [];
                metadata.structured_data.push(data);
            } catch (e) {
                // Ignore invalid JSON
            }
        });

        return metadata;
    }

    extractArticles() {
        const articles = [];
        
        // Common selectors for news articles
        const selectors = [
            '.news-card',
            '.article-card',
            '.card',
            '[data-article]',
            '.news-item',
            '.article-item'
        ];

        selectors.forEach(selector => {
            document.querySelectorAll(selector).forEach(element => {
                const article = this.extractArticleFromElement(element);
                if (article && article.title) {
                    articles.push(article);
                }
            });
        });

        // Remove duplicates based on title
        const uniqueArticles = articles.filter((article, index, self) =>
            index === self.findIndex(a => a.title === article.title)
        );

        return uniqueArticles.slice(0, 20); // Limit to 20 articles
    }

    extractArticleFromElement(element) {
        const article = {};

        // Extract title
        const titleSelectors = ['.card-title', '.article-title', 'h1', 'h2', 'h3', '.title'];
        for (const selector of titleSelectors) {
            const titleEl = element.querySelector(selector);
            if (titleEl) {
                article.title = titleEl.textContent.trim();
                break;
            }
        }

        // Extract summary/content
        const contentSelectors = ['.card-text', '.article-summary', '.summary', '.content', 'p'];
        for (const selector of contentSelectors) {
            const contentEl = element.querySelector(selector);
            if (contentEl) {
                article.summary = contentEl.textContent.trim().substring(0, 300);
                break;
            }
        }

        // Extract link
        const linkEl = element.querySelector('a') || element.closest('a');
        if (linkEl) {
            article.url = linkEl.href;
        }

        // Extract image
        const imgEl = element.querySelector('img');
        if (imgEl) {
            article.image_url = imgEl.src;
        }

        // Extract metadata
        const metadata = {};
        element.querySelectorAll('[data-*]').forEach(el => {
            Array.from(el.attributes).forEach(attr => {
                if (attr.name.startsWith('data-')) {
                    metadata[attr.name.substring(5)] = attr.value;
                }
            });
        });
        
        if (Object.keys(metadata).length > 0) {
            article.metadata = metadata;
        }

        // Extract date
        const dateSelectors = ['.date', '.published', '.timestamp', '[datetime]'];
        for (const selector of dateSelectors) {
            const dateEl = element.querySelector(selector);
            if (dateEl) {
                article.date = dateEl.textContent.trim() || dateEl.getAttribute('datetime');
                break;
            }
        }

        return article;
    }

    extractFeaturedArticles() {
        const featured = [];
        
        document.querySelectorAll('.featured-article, .hero-article, .top-story').forEach(element => {
            const article = this.extractArticleFromElement(element);
            if (article && article.title) {
                featured.push(article);
            }
        });

        return featured;
    }

    extractMarketData() {
        const marketData = {};

        // Extract ticker data from the page
        document.querySelectorAll('[data-ticker]').forEach(element => {
            const ticker = element.getAttribute('data-ticker');
            const price = element.querySelector('.price')?.textContent;
            const change = element.querySelector('.change')?.textContent;
            
            if (ticker) {
                marketData[ticker] = { price, change };
            }
        });

        // Extract market overview data
        const overviewElements = document.querySelectorAll('.market-overview .ticker-item');
        overviewElements.forEach(element => {
            const ticker = element.querySelector('.ticker-symbol')?.textContent;
            const price = element.querySelector('.ticker-price')?.textContent;
            const change = element.querySelector('.ticker-change')?.textContent;
            
            if (ticker) {
                marketData[ticker] = { price, change };
            }
        });

        return marketData;
    }

    extractChartData() {
        const charts = [];
        
        // Look for Plotly charts
        document.querySelectorAll('[id*="chart"], .plotly-graph-div').forEach(element => {
            if (element.data && element.layout) {
                charts.push({
                    id: element.id,
                    type: 'plotly',
                    title: element.layout.title?.text || 'Chart'
                });
            }
        });

        return charts;
    }

    extractAdminData() {
        const adminData = {};

        // Extract current admin section
        const activeTab = document.querySelector('.nav-link.active')?.textContent;
        if (activeTab) {
            adminData.active_section = activeTab;
        }

        // Extract table data if present
        const tables = document.querySelectorAll('table');
        if (tables.length > 0) {
            adminData.tables = Array.from(tables).map(table => ({
                headers: Array.from(table.querySelectorAll('th')).map(th => th.textContent.trim()),
                row_count: table.querySelectorAll('tbody tr').length
            }));
        }

        return adminData;
    }

    extractGeneralContent() {
        const content = {};

        // Extract main content
        const mainSelectors = ['main', '.main-content', '.content', '.container'];
        for (const selector of mainSelectors) {
            const mainEl = document.querySelector(selector);
            if (mainEl) {
                content.main_text = mainEl.textContent.trim().substring(0, 1000);
                break;
            }
        }

        // Extract headings
        const headings = [];
        document.querySelectorAll('h1, h2, h3').forEach(heading => {
            headings.push({
                level: heading.tagName.toLowerCase(),
                text: heading.textContent.trim()
            });
        });
        content.headings = headings.slice(0, 10);

        return content;
    }

    extractVisibleText() {
        // Get all visible text content, excluding script and style elements
        const walker = document.createTreeWalker(
            document.body,
            NodeFilter.SHOW_TEXT,
            {
                acceptNode: function(node) {
                    const parent = node.parentElement;
                    if (!parent) return NodeFilter.FILTER_REJECT;
                    
                    // Skip hidden elements
                    const style = window.getComputedStyle(parent);
                    if (style.display === 'none' || style.visibility === 'hidden') {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    // Skip script and style elements
                    const tagName = parent.tagName.toLowerCase();
                    if (['script', 'style', 'noscript'].includes(tagName)) {
                        return NodeFilter.FILTER_REJECT;
                    }
                    
                    return NodeFilter.FILTER_ACCEPT;
                }
            }
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            const text = node.textContent.trim();
            if (text.length > 0) {
                textNodes.push(text);
            }
        }

        return textNodes.join(' ').substring(0, 2000); // Limit to 2000 characters
    }

    extractTickers(text) {
        // Extract stock ticker symbols from text
        const tickerPattern = /\b[A-Z]{1,5}\b/g;
        const matches = text.match(tickerPattern) || [];
        
        // Filter common words that aren't tickers
        const commonWords = new Set(['THE', 'AND', 'FOR', 'ARE', 'BUT', 'NOT', 'YOU', 'ALL', 'CAN', 'HER', 'WAS', 'ONE', 'OUR', 'HAD', 'BY', 'UP', 'DO', 'NO', 'IF', 'MY', 'ON', 'AS', 'WE', 'TO', 'BE', 'AT', 'OR', 'IN', 'IS', 'IT', 'OF', 'SO', 'HE', 'HIS', 'SHE', 'HAS', 'AN']);
        
        const tickers = matches.filter(match => 
            !commonWords.has(match) && 
            match.length >= 2 && 
            match.length <= 5
        );

        // Remove duplicates and return
        return [...new Set(tickers)].slice(0, 10);
    }

    onContextChange(callback) {
        this.contextChangeCallbacks.push(callback);
    }

    notifyContextChange() {
        const newContext = this.extractContext();
        
        // Only notify if context has meaningfully changed
        if (!this.lastContext || this.hasSignificantChange(this.lastContext, newContext)) {
            this.lastContext = newContext;
            this.contextChangeCallbacks.forEach(callback => {
                try {
                    callback(newContext);
                } catch (error) {
                    console.error('Error in context change callback:', error);
                }
            });
        }
    }

    hasSignificantChange(oldContext, newContext) {
        // Check for significant changes that would affect AI responses
        return (
            oldContext.url !== newContext.url ||
            oldContext.title !== newContext.title ||
            oldContext.page_type !== newContext.page_type ||
            JSON.stringify(oldContext.articles) !== JSON.stringify(newContext.articles) ||
            JSON.stringify(oldContext.market_data) !== JSON.stringify(newContext.market_data)
        );
    }

    getCurrentContext() {
        return this.lastContext || this.extractContext();
    }

    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers = [];
        this.contextChangeCallbacks = [];
    }
}

// Initialize context extractor
window.pageContextExtractor = new PageContextExtractor();
