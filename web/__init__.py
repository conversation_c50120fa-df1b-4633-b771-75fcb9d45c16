"""
NewsMonitor - Web Interface Package

A Flask-based web interface for displaying SP500 index graphs and financial news.
"""

__version__ = "0.1.0"

import os
from flask import Flask
from flask_socketio import Socket<PERSON>
from celery import Celery
from celery.schedules import crontab
from cloud.cloud_config import is_cloud_environment
from web.email_service.service import EmailService
from web.extensions import db, login_manager, socketio
from web.email_service.scheduler_endpoints import scheduler_bp
from web.models import User

celery = Celery(__name__)
email_service = EmailService()


def create_app(config_class="web.config.Config"):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize Flask-SQLAlchemy
    db.init_app(app)

    # Initialize Flask-Login
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Please log in to access this page.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        return db.session.get(User, int(user_id))

    # Initialize SocketIO
    socketio.init_app(
        app,
        cors_allowed_origins="*",
        async_mode='threading',
        logger=True,
        engineio_logger=True
    )

    # Initialize chat WebSocket handlers
    from web.chat.websocket_handler import init_chat_websocket
    init_chat_websocket(socketio, app)

    # Initialize email service
    email_service.init_app(app)

    # Initialize Celery with Flask config
    if not app.config.get('DISABLE_CELERY', True):
        init_celery(app)

    # Register authentication blueprint
    from web.auth.routes import auth_bp
    app.register_blueprint(auth_bp)

    # Register chat blueprint
    from web.chat.routes import chat_bp
    app.register_blueprint(chat_bp)

    # Register health check blueprint
    from cloud.health_endpoints import health_bp
    app.register_blueprint(health_bp)

    # Register scheduler endpoints
    app.register_blueprint(scheduler_bp)

    # Store socketio instance for access in other modules
    app.socketio = socketio

    return app

def init_celery(app=None):
    app = app or create_app()
    if is_cloud_environment():
        redis_ip = os.getenv('REDIS_IP', 'localhost')
        redis_url = f"redis://{redis_ip}:6379/0"
    else:
        redis_url = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/0")

    celery.conf.broker_url = redis_url
    celery.conf.result_backend = redis_url
    celery.conf.timezone = 'America/New_York'
    celery.conf.enable_utc = False
    celery.conf.beat_schedule = {
        "send-daily-summary": {
            "task": "web.email_service.scheduler.send_daily_summaries_task",
            "schedule": crontab(hour=10, minute=0),  # every day at 10 AM ET
            'options': {'expires': 60.0 * 60.0 * 2.0}  # Expire after 2 hours
        },
    }

    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context."""

        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
