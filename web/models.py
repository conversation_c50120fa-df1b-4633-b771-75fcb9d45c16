"""
Flask-SQLAlchemy models for the NewsMonitor web application.

This module provides User and related models that work properly with Flask-SQLAlchemy
and Flask-Login, resolving detached instance issues.
"""

from typing import Any, Dict
from flask_sqlalchemy import SQLAlchemy
from sqlalchemy import TIMESTAMP, Boolean, Column, Float, String, Text, DateTime, ForeignKey, Integer, Index, text
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin
from web.extensions import db

class User(db.Model, UserMixin):
    """User model for authentication and user management."""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)

    # Profile information
    first_name = Column(String(50))
    last_name = Column(String(50))

    # This column serves both Flask-Login and DB queries
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    is_admin = Column(Boolean, default=False, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False)
    updated_at = Column(DateTime(timezone=True), nullable=False)
    last_login = Column(DateTime(timezone=True))

    # Email preferences
    email_preferences = Column(JSONB, default=lambda: {
        'market_summary': True,
        'market_alerts': True,
        'frequency': 'daily'  # daily, weekly, never
    })

    # User preferences
    user_preferences = Column(JSONB, default=lambda: {
        'theme': 'light',
        'timezone': 'UTC',
        'language': 'en'
    })

    # Relationships
    email_logs = relationship(
        "EmailLog", back_populates="user", cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index('ix_users_email_active', 'email', 'is_active'),
        Index('ix_users_username_active', 'username', 'is_active'),
        Index('ix_users_created_at', 'created_at'),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<User(username={self.username!r}, email={self.email!r})>"

    def set_password(self, password):
        """Set password hash."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash."""
        return check_password_hash(self.password_hash, password)

    def get_id(self):
        """Return user ID as string for Flask-Login."""
        return str(self.id)

    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary."""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'is_admin': self.is_admin,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_login': self.last_login,
            'email_preferences': self.email_preferences,
            'user_preferences': self.user_preferences
        }

        if include_sensitive:
            data['password_hash'] = self.password_hash

        return data


class EmailLog(db.Model):
    """Email log model for tracking sent emails."""
    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey(
        'users.id', ondelete='CASCADE'), nullable=False)

    # Email details
    # daily_summary, market_alert, etc.
    email_type = Column(String(50), nullable=False)
    subject = Column(String(255), nullable=False)
    recipient_email = Column(String(120), nullable=False)

    # Status and metadata
    status = Column(String(20), default='pending',
                    nullable=False)  # pending, sent, failed
    sent_at = Column(DateTime(timezone=True))
    error_message = Column(Text)

    # Content metadata
    content_hash = Column(String(64))  # For deduplication
    template_version = Column(String(20))

    # Relationships
    user = relationship("User", back_populates="email_logs")

    # Indexes for performance
    __table_args__ = (
        Index('ix_email_logs_user_id', 'user_id'),
        Index('ix_email_logs_status', 'status'),
        Index('ix_email_logs_sent_at', 'sent_at'),
        Index('ix_email_logs_email_type', 'email_type'),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<EmailLog(user_id={self.user_id}, type={self.email_type}, status={self.status})>"

    def to_dict(self):
        """Convert email log to dictionary."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'email_type': self.email_type,
            'subject': self.subject,
            'recipient_email': self.recipient_email,
            'status': self.status,
            'sent_at': self.sent_at,
            'error_message': self.error_message,
            'content_hash': self.content_hash,
            'template_version': self.template_version
        }

class ChatSession(db.Model):
    """Chat session model for tracking user conversations with AI agent."""
    __tablename__ = 'chat_sessions'

    id = Column(String, primary_key=True)
    user_id = Column(Integer, ForeignKey('users.id'), nullable=False)
    session_token = Column(String, nullable=False, unique=True)  # For anonymous session tracking

    # Session metadata
    title = Column(String)  # Auto-generated or user-defined session title
    status = Column(String, default='active')  # 'active', 'archived', 'deleted'

    # Context tracking
    current_page_url = Column(String)  # Last page URL for context
    page_context = Column(JSONB)  # Current page content and metadata

    # Timestamps
    created_at = Column(
        TIMESTAMP,
        server_default=text('CURRENT_TIMESTAMP'),
        nullable=False
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text('CURRENT_TIMESTAMP'),
        nullable=False
    )
    last_activity_at = Column(
        TIMESTAMP,
        server_default=text('CURRENT_TIMESTAMP'),
        nullable=False
    )

    # Relationships
    messages = relationship("ChatMessage", back_populates="session", cascade="all, delete-orphan")

    # Indexes
    __table_args__ = (
        Index('ix_chat_sessions_user_id', 'user_id'),
        Index('ix_chat_sessions_session_token', 'session_token'),
        Index('ix_chat_sessions_status', 'status'),
        Index('ix_chat_sessions_created_at', 'created_at'),
        Index('ix_chat_sessions_last_activity', 'last_activity_at'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'user_id': self.user_id,
            'session_token': self.session_token,
            'title': self.title,
            'status': self.status,
            'current_page_url': self.current_page_url,
            'page_context': self.page_context,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_activity_at': self.last_activity_at
        }


class ChatMessage(db.Model):
    """Chat message model for storing individual messages in conversations."""
    __tablename__ = 'chat_messages'

    id = Column(String, primary_key=True)
    session_id = Column(String, ForeignKey('chat_sessions.id', ondelete='CASCADE'), nullable=False)

    # Message content
    role = Column(String, nullable=False)  # 'user', 'assistant', 'system'
    content = Column(Text, nullable=False)

    # Message metadata
    message_type = Column(String, default='text')  # 'text', 'error', 'system', 'context'
    context_data = Column(JSONB)  # Additional context like referenced articles, market data

    # AI processing metadata
    model_used = Column(String)  # Which LLM model was used for this response
    processing_time = Column(Float)  # Time taken to generate response
    token_count = Column(Integer)  # Token count for the message
    cost = Column(Float)  # Cost of generating this message

    # Status tracking
    status = Column(String, default='sent')  # 'pending', 'sent', 'error', 'deleted'
    error_message = Column(Text)  # Error details if status is 'error'

    # Timestamps
    created_at = Column(
        TIMESTAMP,
        server_default=text('CURRENT_TIMESTAMP'),
        nullable=False
    )
    updated_at = Column(
        TIMESTAMP,
        server_default=text('CURRENT_TIMESTAMP'),
        nullable=False
    )

    # Relationships
    session = relationship("ChatSession", back_populates="messages")

    # Indexes
    __table_args__ = (
        Index('ix_chat_messages_session_id', 'session_id'),
        Index('ix_chat_messages_role', 'role'),
        Index('ix_chat_messages_created_at', 'created_at'),
        Index('ix_chat_messages_status', 'status'),
        Index('ix_chat_messages_model', 'model_used'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'session_id': self.session_id,
            'role': self.role,
            'content': self.content,
            'message_type': self.message_type,
            'context_data': self.context_data,
            'model_used': self.model_used,
            'processing_time': self.processing_time,
            'token_count': self.token_count,
            'cost': self.cost,
            'status': self.status,
            'error_message': self.error_message,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
