/**
 * AI Chat Widget for NewsMonitor
 * 
 * Provides a floating chat interface with real-time WebSocket communication,
 * context-aware responses, and session management.
 */

class ChatWidget {
    constructor() {
        this.isOpen = false;
        this.socket = null;
        this.sessionId = null;
        this.sessionToken = this.getOrCreateSessionToken();
        this.isConnected = false;
        this.isTyping = false;
        this.typingTimeout = null;
        this.messageQueue = [];
        
        this.init();
    }

    init() {
        this.createWidget();
        this.bindEvents();
        this.connectWebSocket();
        this.loadSuggestions();
    }

    getOrCreateSessionToken() {
        let token = localStorage.getItem('chat_session_token');
        if (!token) {
            token = 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            localStorage.setItem('chat_session_token', token);
        }
        return token;
    }

    createWidget() {
        const widget = document.createElement('div');
        widget.className = 'chat-widget';
        widget.innerHTML = `
            <button class="chat-toggle" id="chatToggle">
                <i class="bi bi-chat-dots"></i>
                <div class="chat-notification" id="chatNotification" style="display: none;">1</div>
            </button>
            
            <div class="chat-window" id="chatWindow">
                <div class="chat-header">
                    <div>
                        <h3>AI Assistant</h3>
                        <div class="chat-status">
                            <div class="status-indicator"></div>
                            <span id="chatStatus">Connecting...</span>
                        </div>
                    </div>
                    <button class="chat-close" id="chatClose">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
                
                <div class="chat-messages" id="chatMessages">
                    <div class="chat-loading" id="chatLoading">
                        <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                        Initializing chat...
                    </div>
                </div>
                
                <div class="chat-input-area">
                    <div class="chat-suggestions" id="chatSuggestions"></div>
                    <div class="chat-input-container">
                        <textarea 
                            class="chat-input" 
                            id="chatInput" 
                            placeholder="Ask me about financial news or market data..."
                            rows="1"
                            disabled
                        ></textarea>
                        <button class="chat-send" id="chatSend" disabled>
                            <i class="bi bi-send"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(widget);
        
        // Store references
        this.elements = {
            toggle: document.getElementById('chatToggle'),
            window: document.getElementById('chatWindow'),
            close: document.getElementById('chatClose'),
            messages: document.getElementById('chatMessages'),
            input: document.getElementById('chatInput'),
            send: document.getElementById('chatSend'),
            status: document.getElementById('chatStatus'),
            loading: document.getElementById('chatLoading'),
            suggestions: document.getElementById('chatSuggestions'),
            notification: document.getElementById('chatNotification')
        };
    }

    bindEvents() {
        // Toggle chat window
        this.elements.toggle.addEventListener('click', () => this.toggleChat());
        this.elements.close.addEventListener('click', () => this.closeChat());
        
        // Send message
        this.elements.send.addEventListener('click', () => this.sendMessage());
        
        // Input handling
        this.elements.input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        this.elements.input.addEventListener('input', () => {
            this.handleTyping();
            this.autoResize();
        });
        
        // Close on outside click
        document.addEventListener('click', (e) => {
            if (this.isOpen && !e.target.closest('.chat-widget')) {
                this.closeChat();
            }
        });
        
        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.socket && !this.isConnected) {
                this.connectWebSocket();
            }
        });
        
        // Listen for custom event triggered when articles are loaded
        document.addEventListener('articlesLoaded', () => {
            if (this.socket && this.isConnected && this.sessionId) {
                this.socket.emit('update_context', {
                    page_context: this.getPageContext()
                });
            }
        });

        // Listen for custom event triggered when analysis are loaded
        document.addEventListener('analysisLoaded', () => {
            if (this.socket && this.isConnected && this.sessionId) {
                this.socket.emit('update_context', {
                    page_context: this.getPageContext()
                });
            }
        });
    }

    connectWebSocket() {
        if (this.socket && this.socket.connected) {
            return;
        }

        try {
            // Initialize Socket.IO connection
            this.socket = io('/chat', {
                transports: ['websocket', 'polling'],
                timeout: 10000,
                reconnection: true,
                reconnectionAttempts: 5,
                reconnectionDelay: 1000
            });

            this.socket.on('connect', () => {
                console.log('Chat WebSocket connected');
                this.isConnected = true;
                this.updateStatus('Connected', 'online');
                this.joinSession();
            });

            this.socket.on('disconnect', () => {
                console.log('Chat WebSocket disconnected');
                this.isConnected = false;
                this.updateStatus('Disconnected', 'offline');
            });

            this.socket.on('session_joined', (data) => {
                console.log('Joined chat session:', data.session.id);
                this.sessionId = data.session.id;
                this.loadMessages(data.messages);
                this.enableInput();
                this.hideLoading();
            });

            this.socket.on('new_message', (data) => {
                this.addMessage(data.message);
                this.hideNotification();
            });

            this.socket.on('ai_thinking', (data) => {
                if (data.thinking) {
                    this.showTypingIndicator();
                } else {
                    this.hideTypingIndicator();
                }
            });

            this.socket.on('user_typing', (data) => {
                // Handle other users typing (for future multi-user support)
                console.log('User typing:', data);
            });

            this.socket.on('suggestions', (data) => {
                this.loadSuggestions(data.suggestions);
            });

            this.socket.on('error', (data) => {
                console.error('Chat error:', data);
                this.showError(data.message || 'An error occurred');
            });

            this.socket.on('connect_error', (error) => {
                console.error('Connection error:', error);
                this.updateStatus('Connection failed', 'error');
            });

        } catch (error) {
            console.error('Failed to initialize WebSocket:', error);
            this.updateStatus('Connection failed', 'error');
        }
    }

    joinSession() {
        if (!this.socket || !this.isConnected) return;

        const pageContext = this.getPageContext();
        
        this.socket.emit('join_session', {
            session_token: this.sessionToken,
            page_context: pageContext
        });
    }

    getPageContext() {
        // Extract current page context for AI
        const context = {
            url: window.location.href,
            title: document.title,
            timestamp: new Date().toISOString()
        };

        const newsElements = document.querySelectorAll('.news-list-item, .featured-article');

        // Extract article content if on news pages
        const articles = [];
        newsElements.forEach(card => {
            const title = card.querySelector('.news-list-title, .featured-article-title')?.textContent?.trim();
            const content = card.querySelector('.news-list-summary, .featured-article-summary')?.textContent?.trim();
            const link = card.querySelector('a')?.href;
            
            if (title) {
                articles.push({ title, content, link });
            }
        });

        if (articles.length > 0) {
            context.articles = articles.slice(0, 10); // Limit to 10 articles
        }

        // Extract main content
        const mainContent = document.querySelector('.ai-analysis-content')?.textContent?.trim();
        if (mainContent) {
            context.content = mainContent.substring(0, 2000);
        }

        return context;
    }

    sendMessage() {
        const message = this.elements.input.value.trim();
        if (!message || !this.isConnected || !this.sessionId) return;

        // Add user message to UI immediately
        this.addMessage({
            role: 'user',
            content: message,
            created_at: new Date().toISOString()
        });

        // Clear input
        this.elements.input.value = '';
        this.autoResize();

        // Send via WebSocket
        this.socket.emit('send_message', {
            message: message,
            page_context: this.getPageContext()
        });

        // Clear suggestions after first message
        this.clearSuggestions();
    }

    addMessage(message) {
        const messageEl = document.createElement('div');
        messageEl.className = `message ${message.role}`;
        
        const avatar = message.role === 'user' ? 'U' : 
                      message.role === 'assistant' ? 'AI' : '!';
        
        const time = new Date(message.created_at).toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit'
        });

        messageEl.innerHTML = `
            <div class="message-avatar">${avatar}</div>
            <div class="message-content">
                <div class="message-text">${this.formatMessage(message.content)}</div>
                <div class="message-time">${time}</div>
            </div>
        `;

        this.elements.messages.appendChild(messageEl);
        this.scrollToBottom();

        // Show notification if chat is closed
        if (!this.isOpen) {
            this.showNotification();
        }
    }

    formatMessage(content) {
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    loadMessages(messages) {
        this.elements.messages.innerHTML = '';
        messages.forEach(message => this.addMessage(message));
    }

    showTypingIndicator() {
        const existingIndicator = document.getElementById('typingIndicator');
        if (existingIndicator) return;

        const indicator = document.createElement('div');
        indicator.id = 'typingIndicator';
        indicator.className = 'message assistant';
        indicator.innerHTML = `
            <div class="message-avatar">AI</div>
            <div class="message-content">
                <div class="typing-indicator">
                    <div class="typing-dots">
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                        <div class="typing-dot"></div>
                    </div>
                </div>
            </div>
        `;

        this.elements.messages.appendChild(indicator);
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typingIndicator');
        if (indicator) {
            indicator.remove();
        }
    }

    handleTyping() {
        if (!this.isConnected || !this.sessionId) return;

        if (!this.isTyping) {
            this.isTyping = true;
            this.socket.emit('typing', { typing: true });
        }

        clearTimeout(this.typingTimeout);
        this.typingTimeout = setTimeout(() => {
            this.isTyping = false;
            this.socket.emit('typing', { typing: false });
        }, 1000);
    }

    loadSuggestions(suggestions = null) {
        if (!suggestions) {
            // Request suggestions from server
            if (this.socket && this.isConnected) {
                this.socket.emit('get_suggestions', {
                    page_context: this.getPageContext()
                });
            }
            return;
        }

        this.elements.suggestions.innerHTML = '';
        suggestions.forEach(suggestion => {
            const chip = document.createElement('button');
            chip.className = 'suggestion-chip';
            chip.textContent = suggestion;
            chip.addEventListener('click', () => {
                this.elements.input.value = suggestion;
                this.sendMessage();
            });
            this.elements.suggestions.appendChild(chip);
        });
    }

    clearSuggestions() {
        this.elements.suggestions.innerHTML = '';
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }

    openChat() {
        this.isOpen = true;
        this.elements.window.classList.add('active');
        this.elements.toggle.classList.add('active');
        this.hideNotification();
        
        // Focus input if enabled
        if (!this.elements.input.disabled) {
            setTimeout(() => this.elements.input.focus(), 300);
        }
        
        // Update page context
        if (this.socket && this.isConnected && this.sessionId) {
            this.socket.emit('update_context', {
                page_context: this.getPageContext()
            });
        }
    }

    closeChat() {
        this.isOpen = false;
        this.elements.window.classList.remove('active');
        this.elements.toggle.classList.remove('active');
    }

    enableInput() {
        this.elements.input.disabled = false;
        this.elements.send.disabled = false;
        this.elements.input.placeholder = "Ask me about financial news or market data...";
    }

    hideLoading() {
        this.elements.loading.style.display = 'none';
    }

    showNotification() {
        this.elements.notification.style.display = 'flex';
    }

    hideNotification() {
        this.elements.notification.style.display = 'none';
    }

    updateStatus(text, type = 'online') {
        this.elements.status.textContent = text;
        const indicator = this.elements.status.previousElementSibling;
        indicator.style.background = type === 'online' ? '#2ed573' : 
                                   type === 'offline' ? '#ffa502' : '#ff4757';
    }

    showError(message) {
        const errorEl = document.createElement('div');
        errorEl.className = 'chat-error';
        errorEl.textContent = message;
        
        this.elements.messages.appendChild(errorEl);
        this.scrollToBottom();
        
        // Remove error after 5 seconds
        setTimeout(() => errorEl.remove(), 5000);
    }

    autoResize() {
        const input = this.elements.input;
        input.style.height = 'auto';
        input.style.height = Math.min(input.scrollHeight, 100) + 'px';
    }

    scrollToBottom() {
        this.elements.messages.scrollTop = this.elements.messages.scrollHeight;
    }
}

// Initialize chat widget when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Only initialize if Socket.IO is available
    if (typeof io !== 'undefined') {
        console.log('Socket.IO found, initializing chat widget...');

        window.chatWidget = new ChatWidget();
    } else {
        console.warn('Socket.IO not available, chat widget disabled');

    }
});
