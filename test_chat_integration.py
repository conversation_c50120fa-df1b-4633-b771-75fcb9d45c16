#!/usr/bin/env python3
"""
Test script for AI Chat Integration

This script tests the basic functionality of the AI chat system,
including database operations, API endpoints, and WebSocket connections.
"""

import sys
import os
import asyncio
import json
from datetime import datetime
import uuid
from web.app import app

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web.chat.ai_service import ai_chat_service
from apis.yahoo_finance import yahoo_api


def test_database_connection():
    """Test database connection and chat service."""
    print("Testing database connection...")
    
    try:
        print("✓ Database connection successful")
        
        # Test chat service
        print("✓ Chat service initialized")
        
        return True
    except Exception as e:
        print(f"✗ Database connection failed: {e}")
        return False


def test_chat_service():
    """Test chat service functionality."""
    print("\nTesting chat service...")
    
    try:
        with app.app_context():
            # Create a test session
            session = ai_chat_service.create_session(
                user_id=17, # Test user ID
                session_token=uuid.uuid4(),
                page_context={
                    "url": "http://localhost:5000/",
                    "title": "Test Page",
                    "content": "Test content for AI chat"
                }
            )
            print(f"✓ Created test session: {session['id']}")
            
            # Test adding a message
            message = ai_chat_service.create_message(
                session_id=session['id'],
                role='user',
                content='Hello, this is a test message',
                context_data={'test': True}
            )
            print(f"✓ Added test message: {message['id']}")
            
            # Test getting session history
            history = ai_chat_service.get_session_history(session['id'])
            print(f"✓ Retrieved session history: {len(history['messages'])} messages")
            
            # Clean up
            ai_chat_service.delete_session(session['id'])
            print("✓ Cleaned up test session")
            
            return True
    except Exception as e:
        print(f"✗ Chat service test failed: {e}")
        return False


def test_yahoo_finance_api():
    """Test Yahoo Finance API integration."""
    print("\nTesting Yahoo Finance API...")
    
    try:
        # Test getting latest price
        data = yahoo_api.get_latest_price("SPY")
        if not data.empty:
            latest_price = data['Close'].iloc[-1]
            print(f"✓ Retrieved SPY latest price: ${latest_price:.2f}")
        else:
            print("✗ No data returned from Yahoo Finance")
            return False
        
        # Test getting historical data
        historical = yahoo_api.get_price_data("SPY", period="5d")
        if not historical.empty:
            print(f"✓ Retrieved SPY historical data: {len(historical)} data points")
        else:
            print("✗ No historical data returned")
            return False
        
        return True
    except Exception as e:
        print(f"✗ Yahoo Finance API test failed: {e}")
        return False


async def test_ai_response():
    """Test AI response generation."""
    print("\nTesting AI response generation...")
    
    try:
        with app.app_context():
            # Create a test session
            session = ai_chat_service.create_session(
                user_id=17, # Test user ID
                session_token=uuid.uuid4(),
                page_context={
                    "url": "http://localhost:5000/",
                    "title": "Market Analysis"
                }
            )
            print(f"✓ Created AI test session: {session['id']}")
            
            # Test AI message processing
            result = await ai_chat_service.process_message(
                session_id=session['id'],
                user_message="What can you tell me about the current market trends?",
                page_context={
                    "url": "http://localhost:5000/",
                    "title": "Market Analysis",
                    "content": '''The politicization of Federal Reserve policy and its perceived impact on interest rates,
                    overriding traditional economic indicators. President Trump's aggressive public pressure on Chair Powell 
                    and the speculation of an early, more dovish replacement are driving market expectations for accelerated rate cuts, 
                    leading to a weaker dollar and higher equity valuations. This narrative is so powerful that it is causing the market
                    to interpret negative economic data (GDP contraction, rising jobless claims) as positive for equities 
                    (as it increases rate cut odds) and to largely disregard persistent inflation (higher Core PCE). 
                    This creates a fragile, sentiment-driven rally.'''
                }
            )
            
            if result['success']:
                print("✓ AI response generated successfully")
                print(f"  Response length: {len(result['ai_response']['content'])} characters")
                print(f"  Processing time: {result['processing_time']:.2f} seconds")
            else:
                print(f"✗ AI response failed: {result.get('error', 'Unknown error')}")
                return False
            
            # Clean up
            # ai_chat_service.delete_session(session['id'])
            # print("✓ Cleaned up AI test session")
            
            return True
    except Exception as e:
        print(f"✗ AI response test failed: {e}")
        return False


def test_context_extraction():
    """Test page context extraction functionality."""
    print("\nTesting context extraction...")
    
    try:
        with app.app_context():
            # Simulate page context
            test_context = {
                "url": "http://localhost:5000/",
                "title": "NewsMonitor - Financial News",
                "page_type": "news",
                "articles": [
                    {
                        "title": "Fed Announces Interest Rate Decision",
                        "summary": "The Federal Reserve announced its latest interest rate decision...",
                        "url": "http://example.com/fed-decision"
                    },
                    {
                        "title": "Tech Stocks Rally on Earnings Beat",
                        "summary": "Major technology companies reported strong earnings...",
                        "url": "http://example.com/tech-rally"
                    }
                ],
                "tickers": ["SPY", "QQQ", "AAPL", "MSFT"],
                "visible_text": "Latest financial news and market analysis..."
            }
            
            print("✓ Context extraction simulation successful")
            print(f"  Found {len(test_context['articles'])} articles")
            print(f"  Found {len(test_context['tickers'])} tickers")
            
            return True
    except Exception as e:
        print(f"✗ Context extraction test failed: {e}")
        return False


def test_suggestions():
    """Test suggestion generation."""
    print("\nTesting suggestion generation...")
    
    try:
        with app.app_context():
            # Test getting suggestions
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            suggestions = loop.run_until_complete(
                ai_chat_service.get_suggested_questions({
                    "url": "http://localhost:5000/",
                    "page_type": "news",
                    "articles": [{"title": "Market Update"}]
                })
            )
            
            if suggestions and len(suggestions) > 0:
                print(f"✓ Generated {len(suggestions)} suggestions")
                for i, suggestion in enumerate(suggestions[:3], 1):
                    print(f"  {i}. {suggestion}")
            else:
                print("✗ No suggestions generated")
                return False
            
            return True
    except Exception as e:
        print(f"✗ Suggestion generation test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("AI Chat Integration Test Suite")
    print("=" * 50)
    
    tests = [
        ("Database Connection", test_database_connection),
        ("Chat Service", test_chat_service),
        ("Yahoo Finance API", test_yahoo_finance_api),
        ("Context Extraction", test_context_extraction),
        ("Suggestion Generation", test_suggestions),
        ("AI Response Generation", lambda: asyncio.run(test_ai_response()))
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}:")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✓ {test_name} PASSED")
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! AI Chat integration is ready.")
        return 0
    else:
        print("❌ Some tests failed. Please check the configuration.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
