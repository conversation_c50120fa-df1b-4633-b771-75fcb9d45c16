/* AI Chat Widget Styles */

.chat-widget {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1050;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.chat-toggle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.chat-toggle:hover {
    transform: scale(1.05);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.2);
}

.chat-toggle i {
    color: white;
    font-size: 24px;
    transition: transform 0.3s ease;
}

.chat-toggle.active i {
    transform: rotate(180deg);
}

.chat-notification {
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    background: #ff4757;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    font-weight: bold;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.chat-window {
    position: absolute;
    bottom: 80px;
    right: 0;
    width: 380px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    display: none;
    flex-direction: column;
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0;
    transition: all 0.3s ease;
}

.chat-window.active {
    display: flex;
    transform: translateY(0);
    opacity: 1;
}

.chat-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.chat-status {
    font-size: 12px;
    opacity: 0.9;
    display: flex;
    align-items: center;
    gap: 6px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #2ed573;
    animation: pulse 2s infinite;
}

.chat-close {
    background: none;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background 0.2s ease;
}

.chat-close:hover {
    background: rgba(255, 255, 255, 0.1);
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 16px;
    background: #f8f9fa;
}

.message {
    display: flex;
    gap: 12px;
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: 600;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: #667eea;
    color: white;
}

.message.assistant .message-avatar {
    background: #2ed573;
    color: white;
}

.message.error .message-avatar {
    background: #ff4757;
    color: white;
}

.message-content {
    max-width: 75%;
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: relative;
}

.message.user .message-content {
    background: #667eea;
    color: white;
}

.message.error .message-content {
    background: #ffe0e0;
    color: #d63031;
    border: 1px solid #ff7675;
}

.message-text {
    margin: 0;
    line-height: 1.4;
    word-wrap: break-word;
}

.message-time {
    font-size: 11px;
    opacity: 0.6;
    margin-top: 4px;
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 0;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #667eea;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.chat-input-area {
    padding: 16px 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.chat-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.suggestion-chip {
    background: #f1f3f4;
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    color: #5f6368;
}

.suggestion-chip:hover {
    background: #e8eaed;
    transform: translateY(-1px);
}

.chat-input-container {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 10px 16px;
    font-size: 14px;
    resize: none;
    max-height: 100px;
    min-height: 40px;
    outline: none;
    transition: border-color 0.2s ease;
}

.chat-input:focus {
    border-color: #667eea;
}

.chat-send {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #667eea;
    border: none;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.chat-send:hover:not(:disabled) {
    background: #5a67d8;
    transform: scale(1.05);
}

.chat-send:disabled {
    background: #cbd5e0;
    cursor: not-allowed;
    transform: none;
}

.chat-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #667eea;
}

.chat-error {
    padding: 16px;
    background: #ffe0e0;
    color: #d63031;
    border-radius: 8px;
    margin: 16px 20px;
    font-size: 14px;
    border: 1px solid #ff7675;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
    .chat-widget {
        bottom: 10px;
        right: 10px;
    }
    
    .chat-window {
        width: calc(100vw - 20px);
        height: calc(100vh - 100px);
        bottom: 70px;
        right: -10px;
    }
    
    .chat-toggle {
        width: 50px;
        height: 50px;
    }
    
    .chat-toggle i {
        font-size: 20px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .chat-window {
        background: #2d3748;
        color: white;
    }
    
    .chat-messages {
        background: #1a202c;
    }
    
    .message-content {
        background: #4a5568;
        color: white;
    }
    
    .message.user .message-content {
        background: #667eea;
    }
    
    .chat-input-area {
        background: #2d3748;
        border-top-color: #4a5568;
    }
    
    .chat-input {
        background: #4a5568;
        border-color: #718096;
        color: white;
    }
    
    .chat-input::placeholder {
        color: #a0aec0;
    }
    
    .suggestion-chip {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .suggestion-chip:hover {
        background: #718096;
    }
}
