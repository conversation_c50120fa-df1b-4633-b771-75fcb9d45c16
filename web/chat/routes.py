"""
REST API routes for AI chat functionality.

This module provides HTTP endpoints for chat session management,
message history, and chat configuration.
"""

import uuid
from datetime import datetime
from flask import Blueprint, request, jsonify
from flask_login import current_user

from web.chat.ai_service import ai_chat_service
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)

# Create blueprint
chat_bp = Blueprint('chat', __name__, url_prefix='/api/chat')


@chat_bp.route('/session', methods=['POST'])
def create_session():
    """Create a new chat session."""
    try:
        data = request.get_json() or {}
        
        user_id = current_user.id if current_user.is_authenticated else None
        session_token = data.get('session_token') or str(uuid.uuid4())
        page_context = data.get('page_context')
        
        session = ai_chat_service.create_session(
            user_id=user_id,
            session_token=session_token,
            page_context=page_context
        )
        
        return jsonify({
            'success': True,
            'session': session
        })

    except Exception as e:
        logger.error(f"Error creating chat session: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/session/<session_id>', methods=['GET'])
def get_session(session_id):
    """Get chat session details."""
    try:
        session = ai_chat_service.db.chat_service.get_session(session_id)
        
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404
        
        # Check access permissions
        if session['user_id'] and current_user.is_authenticated:
            if session['user_id'] != current_user.id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        return jsonify({
            'success': True,
            'session': session
        })

    except Exception as e:
        logger.error(f"Error getting session {session_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/session/<session_id>/history', methods=['GET'])
def get_session_history(session_id):
    """Get chat session message history."""
    try:
        limit = request.args.get('limit', 50, type=int)
        offset = request.args.get('offset', 0, type=int)
        
        session = ai_chat_service.db.chat_service.get_session(session_id)
        
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404
        
        # Check access permissions
        if session['user_id'] and current_user.is_authenticated:
            if session['user_id'] != current_user.id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        history = ai_chat_service.get_session_history(session_id, limit=limit)
        
        return jsonify({
            'success': True,
            'session': history['session'],
            'messages': history['messages'],
            'stats': history['stats']
        })

    except Exception as e:
        logger.error(f"Error getting session history {session_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/sessions', methods=['GET'])
def get_user_sessions():
    """Get user's chat sessions."""
    try:
        limit = request.args.get('limit', 20, type=int)
        session_token = request.args.get('session_token')
        
        user_id = current_user.id if current_user.is_authenticated else None
        
        if not user_id and not session_token:
            return jsonify({
                'success': False,
                'error': 'Authentication required or session token needed'
            }), 401
        
        sessions = ai_chat_service.get_user_sessions(
            user_id=user_id,
            session_token=session_token,
            limit=limit
        )
        
        return jsonify({
            'success': True,
            'sessions': sessions
        })

    except Exception as e:
        logger.error(f"Error getting user sessions: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/session/<session_id>', methods=['DELETE'])
def delete_session(session_id):
    """Delete a chat session."""
    try:
        session = ai_chat_service.db.chat_service.get_session(session_id)
        
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404
        
        # Check access permissions
        if session['user_id'] and current_user.is_authenticated:
            if session['user_id'] != current_user.id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        success = ai_chat_service.delete_session(session_id)
        
        return jsonify({
            'success': success,
            'message': 'Session deleted successfully' if success else 'Failed to delete session'
        })

    except Exception as e:
        logger.error(f"Error deleting session {session_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/session/<session_id>/context', methods=['PUT'])
def update_session_context(session_id):
    """Update session page context."""
    try:
        data = request.get_json() or {}
        page_context = data.get('page_context')
        page_url = data.get('page_url')
        
        session = ai_chat_service.db.chat_service.get_session(session_id)
        
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404
        
        success = ai_chat_service.db.chat_service.update_session_activity(
            session_id,
            page_url=page_url,
            page_context=page_context
        )
        
        return jsonify({
            'success': success,
            'message': 'Context updated successfully' if success else 'Failed to update context'
        })

    except Exception as e:
        logger.error(f"Error updating session context {session_id}: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/suggestions', methods=['POST'])
def get_suggestions():
    """Get suggested questions based on page context."""
    try:
        data = request.get_json() or {}
        page_context = data.get('page_context')
        
        # This would normally be async, but for REST API we'll make it sync
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        suggestions = loop.run_until_complete(
            ai_chat_service.get_suggested_questions(page_context)
        )
        
        return jsonify({
            'success': True,
            'suggestions': suggestions
        })

    except Exception as e:
        logger.error(f"Error getting suggestions: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/message', methods=['POST'])
def send_message():
    """Send a message (synchronous endpoint for non-WebSocket clients)."""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'Request data required'
            }), 400
        
        session_id = data.get('session_id')
        message = data.get('message', '').strip()
        page_context = data.get('page_context')
        
        if not session_id:
            return jsonify({
                'success': False,
                'error': 'Session ID required'
            }), 400
        
        if not message:
            return jsonify({
                'success': False,
                'error': 'Message cannot be empty'
            }), 400
        
        # Check session exists and access permissions
        session = ai_chat_service.db.chat_service.get_session(session_id)
        
        if not session:
            return jsonify({
                'success': False,
                'error': 'Session not found'
            }), 404
        
        if session['user_id'] and current_user.is_authenticated:
            if session['user_id'] != current_user.id:
                return jsonify({
                    'success': False,
                    'error': 'Access denied'
                }), 403
        
        # Process message synchronously
        import asyncio
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        result = loop.run_until_complete(
            ai_chat_service.process_message(
                session_id=session_id,
                user_message=message,
                page_context=page_context
            )
        )
        
        return jsonify(result)

    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500


@chat_bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint for chat service."""
    try:
        # Basic health check
        return jsonify({
            'success': True,
            'service': 'chat',
            'status': 'healthy',
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'success': False,
            'service': 'chat',
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500
