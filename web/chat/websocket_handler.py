"""
WebSocket handler for real-time chat communication.

This module provides WebSocket endpoints for the AI chat functionality,
including real-time messaging, typing indicators, and session management.
"""

import json
import traceback
import uuid
from datetime import datetime
from typing import Dict, Set, Optional, Any

from flask import request
from flask_socketio import So<PERSON><PERSON>, emit, join_room, leave_room, disconnect
from flask_login import current_user
from flask import current_app

from web.chat.ai_service import ai_chat_service
from db.helper import to_json_serializable
from utils.logging_config import get_web_logger
from web.extensions import socketio


logger = get_web_logger(__name__)


class ChatWebSocketHandler:
    """WebSocket handler for chat functionality."""

    def __init__(self, socketio_instance: SocketIO, app=None):
        self.socketio = socketio_instance or socketio
        self.app = app
        self.active_sessions: Dict[str, Set[str]] = {}  # session_id -> set of socket_ids
        self.socket_sessions: Dict[str, str] = {}  # socket_id -> session_id
        self.typing_users: Dict[str, Set[str]] = {}  # session_id -> set of socket_ids
        self._setup_handlers()

    def _setup_handlers(self):
        """Set up WebSocket event handlers."""

        @self.socketio.on('connect', namespace='/chat')
        def handle_connect():
            """Handle client connection."""
            socket_id = request.sid
            logger.info(f"Chat client connected: {socket_id}")
            
            # Send connection confirmation
            emit('connected', {
                'socket_id': socket_id,
                'timestamp': datetime.now().isoformat()
            })

        @self.socketio.on('disconnect', namespace='/chat')
        def handle_disconnect():
            """Handle client disconnection."""
            socket_id = request.sid
            logger.info(f"Chat client disconnected: {socket_id}")
            
            # Clean up session tracking
            if socket_id in self.socket_sessions:
                session_id = self.socket_sessions[socket_id]
                self._leave_session(socket_id, session_id)

        @self.socketio.on('join_session', namespace='/chat')
        def handle_join_session(data):
            """Handle joining a chat session."""
            socket_id = request.sid
            session_id = data.get('session_id')
            session_token = data.get('session_token')
            
            if not session_id and not session_token:
                emit('error', {'message': 'Session ID or token required'})
                return

            try:
                # Get or create session
                if session_id:
                    session = ai_chat_service.get_session(session_id)
                else:
                    session = ai_chat_service.get_session_by_token(session_token)

                if not session:
                    # Create new session for anonymous users
                    user_id = current_user.id if current_user.is_authenticated else None
                    session = ai_chat_service.create_session(
                        user_id=user_id,
                        session_token=session_token,
                        page_context=data.get('page_context')
                    )

                session_id = session['id']
                
                # Join the session room
                join_room(session_id)
                self._join_session(socket_id, session_id)
                
                # Get session history
                history = ai_chat_service.get_session_history(session_id, limit=20)
                
                emit('session_joined', to_json_serializable({
                    'session': session,
                    'messages': history['messages'],
                    'stats': history['stats']
                }))
                
                logger.info(f"Socket {socket_id} joined session {session_id}")

            except Exception as e:
                logger.error(f"Error joining session: {e}")
                print(traceback.format_exc())
                emit('error', {'message': f'Failed to join session: {str(e)}'})

        @self.socketio.on('send_message', namespace='/chat')
        def handle_send_message(data):
            """Handle sending a message."""
            socket_id = request.sid
            
            if socket_id not in self.socket_sessions:
                emit('error', {'message': 'Not in a session'})
                return

            session_id = self.socket_sessions[socket_id]
            message = data.get('message', '').strip()
            page_context = data.get('page_context')
            
            if not message:
                emit('error', {'message': 'Message cannot be empty'})
                return

            try:
                # Emit typing stopped for this user
                self._stop_typing(socket_id, session_id)
                
                # Emit message received confirmation
                emit('message_received', {
                    'message': to_json_serializable(message),
                    'timestamp': datetime.now().isoformat()
                })
                
                # Emit AI thinking indicator to all session participants
                self.socketio.emit('ai_thinking', {
                    'session_id': session_id,
                    'thinking': True
                }, room=session_id, namespace='/chat')
                
                # Process message
                self.socketio.start_background_task(
                    self._process_message,
                    session_id,
                    message,
                    page_context
                )

            except Exception as e:
                logger.error(f"Error handling message: {e}")
                emit('error', {'message': f'Failed to send message: {str(e)}'})

        @self.socketio.on('typing', namespace='/chat')
        def handle_typing(data):
            """Handle typing indicator."""
            socket_id = request.sid
            
            if socket_id not in self.socket_sessions:
                return

            session_id = self.socket_sessions[socket_id]
            is_typing = data.get('typing', False)
            
            if is_typing:
                self._start_typing(socket_id, session_id)
            else:
                self._stop_typing(socket_id, session_id)

        @self.socketio.on('update_context', namespace='/chat')
        def handle_update_context(data):
            """Handle page context updates."""
            socket_id = request.sid
            
            if socket_id not in self.socket_sessions:
                return

            session_id = self.socket_sessions[socket_id]
            page_context = data.get('page_context')
            
            try:
                ai_chat_service.update_session_context(session_id, page_context)
                
                emit('context_updated', {'success': True})

            except Exception as e:
                logger.error(f"Error updating context: {e}")
                emit('error', {'message': f'Failed to update context: {str(e)}'})

        @self.socketio.on('get_suggestions', namespace='/chat')
        def handle_get_suggestions(data):
            """Handle getting suggested questions."""
            try:
                page_context = data.get('page_context')
                
                # Run function in background task
                self.socketio.start_background_task(
                    self._get_suggestions,
                    request.sid,
                    page_context
                )

            except Exception as e:
                logger.error(f"Error getting suggestions: {e}")
                emit('error', {'message': f'Failed to get suggestions: {str(e)}'})

    def _join_session(self, socket_id: str, session_id: str):
        """Track socket joining a session."""
        if session_id not in self.active_sessions:
            self.active_sessions[session_id] = set()
        
        self.active_sessions[session_id].add(socket_id)
        self.socket_sessions[socket_id] = session_id

    def _leave_session(self, socket_id: str, session_id: str):
        """Track socket leaving a session."""
        if session_id in self.active_sessions:
            self.active_sessions[session_id].discard(socket_id)
            if not self.active_sessions[session_id]:
                del self.active_sessions[session_id]
        
        if socket_id in self.socket_sessions:
            del self.socket_sessions[socket_id]
        
        # Stop typing if user was typing
        self._stop_typing(socket_id, session_id)
        
        leave_room(session_id)

    def _start_typing(self, socket_id: str, session_id: str):
        """Handle user starting to type."""
        if session_id not in self.typing_users:
            self.typing_users[session_id] = set()
        
        self.typing_users[session_id].add(socket_id)
        
        # Emit to other users in the session
        self.socketio.emit('user_typing', {
            'socket_id': socket_id,
            'typing': True
        }, room=session_id, namespace='/chat', include_self=False)

    def _stop_typing(self, socket_id: str, session_id: str):
        """Handle user stopping typing."""
        if session_id in self.typing_users:
            self.typing_users[session_id].discard(socket_id)
            if not self.typing_users[session_id]:
                del self.typing_users[session_id]
        
        # Emit to other users in the session
        self.socketio.emit('user_typing', {
            'socket_id': socket_id,
            'typing': False
        }, room=session_id, namespace='/chat', include_self=False)

    def _process_message(self, session_id: str, message: str, page_context: Optional[Dict[str, Any]]):
        """Process message with Flask application context."""
        logger.info(f"Processing message for session {session_id}")
        
        # Get the Flask application instance
        with self.app.app_context():
            try:
                # Process the message
                result = ai_chat_service.process_message(
                    session_id=session_id,
                    user_message=message,
                    page_context=page_context
                )
                
                # Stop AI thinking indicator
                self.socketio.emit('ai_thinking', {
                    'session_id': session_id,
                    'thinking': False
                }, room=session_id, namespace='/chat')
                
                if result['success']:
                    # Emit the AI response to all session participants
                    self.socketio.emit('new_message', {
                        'message': to_json_serializable(result['ai_response']),
                        'processing_time': result['processing_time']
                    }, room=session_id, namespace='/chat')
                else:
                    # Emit error message
                    self.socketio.emit('new_message', {
                        'message': result.get('error_message', {
                            'role': 'assistant',
                            'content': 'I apologize, but I encountered an error processing your request.',
                            'message_type': 'error'
                        })
                    }, room=session_id, namespace='/chat')

            except Exception as e:
                logger.error(f"Error in message processing: {e}")
                logger.error(traceback.format_exc())
                
                # Stop AI thinking indicator
                self.socketio.emit('ai_thinking', {
                    'session_id': session_id,
                    'thinking': False
                }, room=session_id, namespace='/chat')
                
                # Emit error
                self.socketio.emit('error', {
                    'message': f'Failed to process message: {str(e)}'
                }, room=session_id, namespace='/chat')

    def _get_suggestions(self, socket_id: str, page_context: Optional[Dict[str, Any]]):
        """Get suggestions with Flask application context."""
        
        # Get the Flask application instance
        with self.app.app_context():
            try:
                suggestions = ai_chat_service.get_suggested_questions(page_context)
                
                self.socketio.emit('suggestions', {
                    'suggestions': suggestions
                }, room=socket_id, namespace='/chat')

            except Exception as e:
                logger.error(f"Error getting suggestions: {e}")
                logger.error(traceback.format_exc())
                self.socketio.emit('error', {
                    'message': f'Failed to get suggestions: {str(e)}'
                }, room=socket_id, namespace='/chat')

# Global handler instance (will be initialized in app factory)
chat_websocket_handler = None


def init_chat_websocket(socketio: SocketIO, app=None):
    """Initialize the chat WebSocket handler."""
    global chat_websocket_handler
    chat_websocket_handler = ChatWebSocketHandler(socketio, app)
    return chat_websocket_handler
