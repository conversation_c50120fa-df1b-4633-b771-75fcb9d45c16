"""
AI Chat Service for NewsMonitor.

This module provides the core AI chat functionality, including:
- LLM integration for generating responses
- Context injection from current page content
- Integration with MCP server for data access
- Session management and message handling
"""

import time
import traceback
from typing import Any, Dict, List, Optional, Tuple
import uuid

from sqlalchemy import func

from apis.llm.gemini import GeminiManager
from apis.llm.openai import OpenAIManager
from apis.llm.anthropic import Anthropic<PERSON>anager
from apis.llm.data_types import CompletionRequest

from web.models import ChatMessage, ChatSession
from web.extensions import db
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)


class AIChatService:
    """Core AI chat service for handling conversations."""

    def __init__(self):
        self.llm_managers = {
            'gemini': GeminiManager(total_budget=10.0),
            'openai': OpenAIManager(total_budget=10.0),
            'anthropic': AnthropicManager(total_budget=10.0)
        }
        self.default_provider = 'gemini'
        self.system_prompt = self._get_system_prompt()

    def _get_system_prompt(self) -> str:
        """Get the system prompt for the AI chat agent."""
        return """You are an AI assistant for CognitMarket, a financial news and market analysis platform. 

Your role is to help users understand financial news, market data, and investment insights. You have access to:

1. **Financial News Database**: Recent articles from various financial news sources
2. **Market Data**: Real-time and historical stock prices, market indicators via Yahoo Finance
3. **Market Analysis**: Predictions and analysis from the platform's AI models

**Guidelines:**
- Provide accurate, helpful information about financial markets and news
- Use the available tools to access current data when answering questions
- Be clear about the limitations of any analysis or predictions
- Always cite sources when referencing specific articles or data
- If you don't have current information, suggest how the user can find it
- Be conversational but professional
- Focus on educational content rather than specific investment advice

**Current Page Context**: The user is currently viewing content that may be relevant to their questions. Use this context to provide more targeted responses.

Remember: This is for educational purposes only and should not be considered as financial advice."""

    def process_message(
        self,
        session_id: str,
        user_message: str,
        id: Optional[str] = None,
        page_context: Optional[Dict[str, Any]] = None,
        preferred_provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """Process a user message and generate an AI response."""
        start_time = time.time()
        
        try:
            # Get or create session
            session = ChatSession.query.get(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")

            # Update session activity and context
            if page_context:
                ChatSession.query.filter_by(id=session_id).update({
                    'current_page_url': page_context.get('url'),
                    'page_context': page_context
                })
                db.session.commit()

            # Add user message to database
            user_msg = ChatMessage(
                id=id or str(uuid.uuid4()),
                session_id=session_id,
                role='user',
                content=user_message,
                context_data=page_context
            )
            db.session.add(user_msg)

            # Generate AI response
            response_content, model_info = self._generate_response(
                session_id=session_id,
                user_message=user_message,
                page_context=page_context,
                preferred_provider=preferred_provider
            )

            processing_time = time.time() - start_time

            # Add AI response to database
            ai_msg = ChatMessage(
                id=id or str(uuid.uuid4()),
                session_id=session_id,
                role='assistant',
                content=response_content,
                model_used=model_info.get('model'),
                processing_time=processing_time,
                token_count=model_info.get('tokens'),
                cost=model_info.get('cost')
            )
            db.session.add(ai_msg)

            db.session.commit()

            return {
                'success': True,
                'user_message': user_msg.to_dict(),
                'ai_response': ai_msg.to_dict(),
                'processing_time': processing_time
            }

        except Exception as e:
            logger.error(f"Error processing message for session {session_id}: {e}")
            logger.error(traceback.format_exc())
            
            # Add error message to database
            try:
                error_msg = ChatMessage(
                    id=id or str(uuid.uuid4()),
                    session_id=session_id,
                    role='assistant',
                    content=f"I apologize, but I encountered an error processing your request: {str(e)}",
                    message_type='error',
                    status='error',
                    error_message=str(e)
                )
                db.session.add(error_msg)
                db.session.commit()
                
                return {
                    'success': False,
                    'error': str(e),
                    'error_message': error_msg.to_dict()
                }
            except:
                return {
                    'success': False,
                    'error': str(e)
                }

    def update_session_context(self, session_id: str, page_context: Dict[str, Any]) -> bool:
        """Update session page context."""
        try:
            ChatSession.query.filter_by(id=session_id).update({
                'current_page_url': page_context.get('url'),
                'page_context': page_context
            })
            db.session.commit()
            return True

        except Exception as e:
            logger.error(f"Error updating session context {session_id}: {e}")
            raise

    def _generate_response(
        self,
        session_id: str,
        user_message: str,
        page_context: Optional[Dict[str, Any]] = None,
        preferred_provider: Optional[str] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """Generate AI response using LLM."""
        
        # Get conversation history
        messages = ChatMessage.query.filter_by(session_id=session_id).order_by(ChatMessage.created_at.desc()).limit(10).all()
        
        # Build context
        context_parts = []
        
        # Add page context if available
        if page_context:
            context_parts.append(f"**Current Page Context:**")
            if page_context.get('title'):
                context_parts.append(f"Page Title: {page_context['title']}")
            if page_context.get('content'):
                # Truncate content to avoid token limits
                content = page_context['content'][:2000]
                context_parts.append(f"Page Content: {content}")
            if page_context.get('articles'):
                for article in page_context['articles']:
                    context_parts.append(f"Article: {article['title']}")

        # Add conversation history
        if messages:
            context_parts.append("\n**Recent Conversation:**")
            for msg in messages[-6:]:  # Last 6 messages for context
                role = msg.role.title()
                content = msg.content[:500]  # Truncate long messages
                context_parts.append(f"{role}: {content}")

        context_text = "\n".join(context_parts)
        
        # Select LLM provider
        provider = preferred_provider or self.default_provider
        if provider not in self.llm_managers:
            provider = self.default_provider

        llm_manager = self.llm_managers[provider]

        # Create completion request
        request = CompletionRequest(
            model=self._get_model_for_provider(provider),
            system_prompt=self.system_prompt,
            user_prompt=f"{context_text}\n\nUser: {user_message}",
            max_tokens=1000,
            temperature=0.1
        )
        logger.info(f"system prompt: {self.system_prompt}")
        logger.info(f"user prompt: {context_text}\n\nUser: {user_message}")

        # Get completion
        completion = llm_manager.get_completion(request)
        
        if not completion:
            raise Exception("Failed to get response from LLM")

        model_info = {
            'model': completion.model,
            'tokens': completion.input_tokens + completion.output_tokens,
            'cost': completion.cost,
            'provider': provider
        }

        return completion.content, model_info

    def _get_model_for_provider(self, provider: str) -> str:
        """Get the default model for a provider."""
        models = {
            'gemini': 'gemini-2.0-flash',
            'openai': 'gpt-4o-mini',
            'anthropic': 'claude-3-haiku-20240307'
        }
        return models.get(provider, 'gemini-2.0-flash')

    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get a chat session."""
        try:
            session = ChatSession.query.get(session_id)
            if not session:
                return None
            return session.to_dict()

        except Exception as e:
            logger.error(f"Error getting session {session_id}: {e}")
            raise
    
    def get_session_by_token(self, session_token: str) -> Optional[Dict[str, Any]]:
        """Get a chat session by token."""
        try:
            session = ChatSession.query.filter_by(session_token=session_token).first()
            if not session:
                return None
            return session.to_dict()

        except Exception as e:
            logger.error(f"Error getting session by token {session_token}: {e}")
            raise

    def create_session(
        self,
        user_id: int,
        id: Optional[str] = None,
        session_token: Optional[str] = None,
        page_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new chat session."""
        try:
            session = ChatSession(
                id=id or str(uuid.uuid4()),
                user_id=user_id,
                session_token=session_token,
                title="New Chat",
                current_page_url=page_context.get('url') if page_context else None,
                page_context=page_context
            )
            db.session.add(session)
            db.session.commit()
            
            logger.info(f"Created new chat session {session.id}")
            return session.to_dict()
        
        except Exception as e:
            logger.error(f"Error creating chat session: {e}")
            raise
    
    def create_message(
        self,
        session_id: str,
        role: str,
        content: str,
        context_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Create a new chat message."""
        try:
            message = ChatMessage(
                id=str(uuid.uuid4()),
                session_id=session_id,
                role=role,
                content=content,
                context_data=context_data
            )
            db.session.add(message)
            db.session.commit()
            
            logger.info(f"Created new chat message {message.id}")
            return message.to_dict()
            
        except Exception as e:
            logger.error(f"Error creating chat message: {e}")
            raise

    def get_session_history(
        self,
        session_id: str,
        limit: int = 50
    ) -> Dict[str, Any]:
        """Get chat session history."""
        try:
            session = ChatSession.query.get(session_id)
            if not session:
                raise ValueError(f"Session {session_id} not found")

            messages = ChatMessage.query.filter_by(session_id=session_id).order_by(
                ChatMessage.created_at.desc()).limit(limit).all()
            stats = ChatMessage.query.filter_by(session_id=session_id).with_entities(
                func.count(ChatMessage.id), func.sum(ChatMessage.token_count), func.sum(ChatMessage.cost)).first()

            return {
                'session': session.to_dict(),
                'messages': [msg.to_dict() for msg in messages],
                'stats': tuple(stats)
            }

        except Exception as e:
            logger.error(f"Error getting session history {session_id}: {e}")
            raise

    def get_user_sessions(
        self,
        user_id: Optional[int] = None,
        session_token: Optional[str] = None,
        limit: int = 20
    ) -> List[Dict[str, Any]]:
        """Get user's chat sessions."""
        try:
            sessions = ChatSession.query.filter_by(user_id=user_id).order_by(ChatSession.created_at.desc()).limit(limit).all()
            return [session.to_dict() for session in sessions]

        except Exception as e:
            logger.error(f"Error getting user sessions: {e}")
            raise

    def delete_session(self, session_id: str) -> bool:
        """Delete a chat session."""
        try:
            success = ChatSession.query.filter_by(id=session_id).delete() > 0
            db.session.commit()
            if success:
                logger.info(f"Deleted chat session {session_id}")
            return success

        except Exception as e:
            logger.error(f"Error deleting session {session_id}: {e}")
            raise

    def get_suggested_questions(
        self,
        page_context: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """Generate suggested questions based on page context."""
        suggestions = [
            "What are the latest market trends?",
            "Can you analyze the current market sentiment?",
            "What should I know about today's financial news?"
        ]

        if page_context:
            if page_context.get('articles'):
                suggestions.insert(0, "Can you summarize the articles on this page for me?")
            
            if 'market' in page_context.get('url', '').lower():
                suggestions.insert(0, "What's the current market outlook?")

        return suggestions[:4]  # Return top 4 suggestions


# Global instance
ai_chat_service = AIChatService()
